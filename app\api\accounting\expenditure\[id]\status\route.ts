// app/api/accounting/expenditure/[id]/status/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getCurrentUser } from '@/lib/backend/auth/auth';
import { connectToDatabase } from '@/lib/database';
import { logger } from '@/lib/utils/logger';
import { z } from 'zod';
import Expenditure from '@/models/accounting/Expenditure';
import { hasRequiredPermissions } from '@/lib/backend/auth/permissions';
import { UserRole } from '@/types/user-roles';
import budgetIntegrationService from '@/lib/services/accounting/budget-integration-service';

export const runtime = 'nodejs';

// Status update validation schema
const statusUpdateSchema = z.object({
  status: z.enum(['draft', 'submitted', 'pending_approval', 'approved', 'rejected', 'paid', 'cancelled', 'on_hold']),
  notes: z.string().optional(),
  reason: z.string().optional(), // For rejections
});

export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Resolve the params promise
    const { id } = await params;
    
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions - only specific roles can approve expenditures
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.HR_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions to update expenditure status' },
        { status: 403 }
      );
    }

    // Get request body
    const data = await req.json();

    // Validate request body
    const validationResult = statusUpdateSchema.safeParse(data);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: validationResult.error.errors[0].message },
        { status: 400 }
      );
    }

    // Find the expenditure record
    const expenditure = await Expenditure.findById(id);
    if (!expenditure) {
      return NextResponse.json(
        { error: 'Expenditure record not found' },
        { status: 404 }
      );
    }

    // Store previous status for logging
    const previousStatus = expenditure.status;
    const newStatus = validationResult.data.status;

    // Validate status transition
    const validTransitions: Record<string, string[]> = {
      'draft': ['submitted', 'approved', 'cancelled'],
      'submitted': ['pending_approval', 'approved', 'rejected', 'cancelled'],
      'pending_approval': ['approved', 'rejected', 'on_hold', 'cancelled'],
      'approved': ['paid', 'cancelled', 'on_hold'],
      'rejected': ['draft', 'cancelled'],
      'paid': ['cancelled'], // Can only cancel paid expenditures
      'on_hold': ['pending_approval', 'approved', 'cancelled'],
      'cancelled': [] // Cannot change from cancelled
    };

    if (!validTransitions[previousStatus]?.includes(newStatus)) {
      return NextResponse.json(
        { error: `Invalid status transition from ${previousStatus} to ${newStatus}` },
        { status: 400 }
      );
    }

    // Update expenditure status
    expenditure.status = newStatus;
    expenditure.updatedBy = user.id;
    expenditure.updatedAt = new Date();

    // Add status change to approval workflow
    if (!expenditure.approvalWorkflow) {
      expenditure.approvalWorkflow = [];
    }

    expenditure.approvalWorkflow.push({
      step: expenditure.approvalWorkflow.length + 1,
      approverRole: user.role || 'Unknown',
      approverId: user.id,
      approverName: user.name || user.email,
      action: newStatus,
      comments: validationResult.data.notes || '',
      timestamp: new Date(),
      isCompleted: true
    });

    // Set specific timestamps based on status
    switch (newStatus) {
      case 'submitted':
        expenditure.submittedAt = new Date();
        expenditure.submittedBy = user.id;
        break;
      case 'approved':
        expenditure.approvedAt = new Date();
        expenditure.approvedBy = user.id;
        break;
      case 'paid':
        expenditure.paidAt = new Date();
        expenditure.paidBy = user.id;
        break;
      case 'rejected':
        expenditure.rejectedAt = new Date();
        expenditure.rejectedBy = user.id;
        expenditure.rejectionReason = validationResult.data.reason || '';
        break;
      case 'cancelled':
        expenditure.cancelledAt = new Date();
        expenditure.cancelledBy = user.id;
        break;
    }

    // Save the updated expenditure
    await expenditure.save();

    // Handle budget integration for approved/paid expenditures
    if ((newStatus === 'paid' || newStatus === 'approved') && expenditure.appliedToBudget) {
      try {
        console.log('Applying expenditure to budget after status change...');
        await budgetIntegrationService.handleNewExpense(expenditure);
        console.log('Expenditure applied to budget successfully');
      } catch (budgetError) {
        console.warn('Budget integration failed after status change:', budgetError);
        // Don't fail the status update if budget integration fails
      }
    }

    // Populate the updated expenditure for response
    const populatedExpenditure = await Expenditure.findById(expenditure._id)
      .populate('createdBy', 'name email')
      .populate('updatedBy', 'name email')
      .populate('approvedBy', 'name email')
      .populate('budget', 'name fiscalYear')
      .populate('budgetCategory', 'name type')
      .lean();

    // Log the status change
    logger.info('Expenditure status updated', {
      expenditureId: expenditure._id,
      previousStatus,
      newStatus,
      updatedBy: user.id,
      userEmail: user.email
    });

    return NextResponse.json({
      success: true,
      message: `Expenditure status updated from ${previousStatus} to ${newStatus}`,
      expenditure: populatedExpenditure,
      statusChange: {
        from: previousStatus,
        to: newStatus,
        updatedBy: user.name || user.email,
        updatedAt: new Date()
      }
    });

  } catch (error: unknown) {
    console.error('Error updating expenditure status:', error);
    logger.error('Error updating expenditure status', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Internal Server Error';
    
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}

// GET method to fetch expenditure with status history
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Resolve the params promise
    const { id } = await params;
    
    // Check authentication
    const user = await getCurrentUser(req);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Connect to database
    await connectToDatabase();

    // Check permissions
    const hasPermission = hasRequiredPermissions(user, [
      UserRole.SUPER_ADMIN,
      UserRole.SYSTEM_ADMIN,
      UserRole.FINANCE_MANAGER,
      UserRole.ACCOUNTANT,
      UserRole.HR_MANAGER
    ]);

    if (!hasPermission) {
      return NextResponse.json(
        { error: 'Forbidden: Insufficient permissions' },
        { status: 403 }
      );
    }

    // Find the expenditure record with full details
    const expenditure = await Expenditure.findById(id)
      .populate('createdBy', 'name email')
      .populate('updatedBy', 'name email')
      .populate('approvedBy', 'name email')
      .populate('budget', 'name fiscalYear')
      .populate('budgetCategory', 'name type')
      .lean();

    if (!expenditure) {
      return NextResponse.json(
        { error: 'Expenditure record not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      expenditure,
      availableActions: getAvailableActions(expenditure.status),
      approvalWorkflow: expenditure.approvalWorkflow || []
    });

  } catch (error: unknown) {
    console.error('Error fetching expenditure status details:', error);
    logger.error('Error fetching expenditure status details', error);
    
    const errorMessage = error instanceof Error ? error.message : 'Internal Server Error';
    
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}

// Helper function to get available actions based on current status
function getAvailableActions(currentStatus: string): string[] {
  const actions: Record<string, string[]> = {
    'draft': ['Submit', 'Approve', 'Cancel'],
    'submitted': ['Approve', 'Reject', 'Put on Hold', 'Cancel'],
    'pending_approval': ['Approve', 'Reject', 'Put on Hold', 'Cancel'],
    'approved': ['Mark as Paid', 'Put on Hold', 'Cancel'],
    'rejected': ['Resubmit as Draft', 'Cancel'],
    'paid': ['Cancel'],
    'on_hold': ['Resume Approval', 'Approve', 'Cancel'],
    'cancelled': []
  };

  return actions[currentStatus] || [];
}
